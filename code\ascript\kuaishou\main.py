import time
import random
from config import *
from hid_control import (
    connect_hid, 
    start_app, 
    swipe_to_next_video, 
    like_video, 
    follow_user,
    add_comment,
    collect_coins,
    watch_video_with_interaction,
    browse_comment_section,
    is_stuck,
    recover_from_stuck
)

def main():
    """主函数，控制整个流程"""
    print("开始运行快手极速版自动化脚本")
    
    # 连接蓝牙HID设备
    ble = connect_hid()
    if not ble:
        print("连接HID设备失败，请检查设备是否已配对")
        return
    
    # 启动快手极速版
    start_app()
    time.sleep(5)  # 等待应用启动
    
    # 主循环
    video_count = 0
    stuck_count = 0
    
    while video_count < MAX_VIDEOS:
        print(f"\n===== 正在观看第 {video_count + 1} 个视频 =====")
        
        # 检查是否卡住
        if is_stuck(ble):
            print("检测到可能卡住了，尝试恢复...")
            if recover_from_stuck(ble):
                stuck_count = 0
            else:
                stuck_count += 1
                if stuck_count >= 3:
                    print("连续3次恢复失败，退出脚本")
                    break
            continue
        
        # 观看视频并进行互动
        watch_video_with_interaction(ble)
        
        # 随机决定是否点赞
        if random.random() < LIKE_PROBABILITY:
            like_video(ble)
        
        # 随机决定是否关注
        if random.random() < FOLLOW_PROBABILITY:
            follow_user(ble)
        
        # 随机决定是否评论
        if random.random() < COMMENT_PROBABILITY:
            add_comment(ble)
        
        # 随机决定是否浏览评论区
        if random.random() < BROWSE_COMMENTS_PROBABILITY:
            print("随机浏览评论区")
            browse_comment_section(ble)
        
        # 随机决定是否收集金币
        if random.random() < COLLECT_COINS_PROBABILITY:
            collect_coins(ble)
        
        # 滑动到下一个视频
        swipe_to_next_video(ble)
        
        # 增加视频计数
        video_count += 1
        
        # 随机等待一段时间再继续
        time.sleep(random.uniform(0.5, 2))
    
    print(f"已完成 {video_count} 个视频的浏览，脚本结束")

if __name__ == "__main__":
    main() 