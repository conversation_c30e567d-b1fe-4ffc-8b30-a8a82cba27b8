#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
快手极速版 ESP32-HID 控制脚本配置文件
可以根据需要修改下面的参数
"""

# 应用包名
APP_PACKAGE = "com.kuaishou.nebula"

# 视频观看配置
MIN_VIDEO_TIME = 10  # 最短观看时间（秒）
MAX_VIDEO_TIME = 25  # 最长观看时间（秒）
MAX_VIDEOS = 999     # 最大视频观看数量

# 功能开关
ENABLE_COMMENTING = False     # 是否启用评论功能
ENABLE_BROWSING = False       # 是否启用浏览功能（浏览评论区等）

# 操作概率配置
LIKE_PROBABILITY = 0.6        # 点赞概率
FOLLOW_PROBABILITY = 0.0 if not ENABLE_BROWSING else 0.2      # 关注概率
COMMENT_PROBABILITY = 0.0 if not ENABLE_COMMENTING else 0.01  # 评论概率
BROWSE_COMMENTS_PROBABILITY = 0.0 if not ENABLE_BROWSING else 0.01  # 浏览评论区概率
COLLECT_COINS_PROBABILITY = 0.8    # 收集金币概率

# 评论内容列表
COMMENTS = [
    "太有趣了",
    "哈哈哈笑死我了",
    "学到了学到了",
    "这个视频太棒了",
    "支持一下",
    "厉害了",
    "真不错",
    "666",
    "继续加油",
    "很喜欢"
]

# 防卡住配置
MAX_STUCK_RECOVERY_ATTEMPTS = 3  # 最大卡住恢复尝试次数
STUCK_CHECK_INTERVAL = 30        # 卡住检查间隔（秒）

# 高级设置
AUTO_WATCH_ADS = False  # 是否自动观看广告
AUTO_COLLECT_COINS = False  # 是否自动收取金币
MAX_RUN_TIME = 10 * 60  # 最大运行时间(秒)，默认10分钟 