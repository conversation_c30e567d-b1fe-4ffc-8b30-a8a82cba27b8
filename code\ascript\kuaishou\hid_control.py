#!/usr/bin/env python
# -*- coding: utf-8 -*-

from ascript.android import plug
from ascript.android import system
from ascript.android.screen import capture
from ascript.android.system import Device
import time
import random
import datetime

# 导入配置
from kuaishou.config import *

# 加载ESP32插件
plug.load("esp32")
from esp32 import BleDevice

# 全局变量存储屏幕尺寸，避免重复获取
SCREEN_WIDTH = None
SCREEN_HEIGHT = None

def set_ascript_input_method():
    """设置ascript输入法为默认输入法"""
    try:
        print("设置ascript输入法为默认输入法...")
        # 使用系统命令设置ascript输入法
        system.shell("ime set com.airscript.ime/.AirScriptInputMethod")
        time.sleep(0.5)
        print("ascript输入法设置成功")
        return True
    except Exception as e:
        print(f"设置ascript输入法出错: {e}")
        return False

def connect_hid():
    """连接ESP32 HID设备"""
    print("正在连接ESP32 HID设备...")
    # 自动扫描AS设备并连接
    ble = BleDevice()
    
    # 检查连接状态
    if ble.is_conncted():
        print("HID设备连接成功")
        print("设备名称:", ble.get_name())
        print("MAC地址:", ble.get_mac_address())
        return ble
    else:
        # 尝试重新连接
        if ble.re_connect():
            print("HID设备重新连接成功")
            return ble
        else:
            print("HID设备连接失败，请检查设备是否已配对")
            return None

def start_app():
    """启动快手极速版应用"""
    print("正在启动快手极速版...")
    # 先按Home键回到桌面
    try:
        # 尝试导入intent
        try:
            # 使用新的方法启动应用
            from airscript.intent import Intent
            print(f"使用Intent启动应用: {APP_PACKAGE}")
            Intent.run(APP_PACKAGE)
        except ImportError:
            # 如果找不到airscript.intent，尝试使用shell命令
            print(f"使用shell命令启动应用: {APP_PACKAGE}")
            system.shell(f"am start -n {APP_PACKAGE}/com.yxcorp.gifshow.HomeActivity")
    except Exception as e:
        print(f"启动应用出错: {e}")
        print("请确保快手极速版已安装，或者尝试手动启动")
    
    # 等待应用启动
    time.sleep(5)
    print("快手极速版已启动")
    return True

def get_screen_size():
    """获取屏幕尺寸，使用官方推荐的Device.display()方法
    
    Returns:
        tuple: (width, height) 屏幕宽度和高度
    """
    global SCREEN_WIDTH, SCREEN_HEIGHT
    
    # 如果已经获取过屏幕尺寸，直接返回缓存的值
    if SCREEN_WIDTH is not None and SCREEN_HEIGHT is not None:
        return SCREEN_WIDTH, SCREEN_HEIGHT
    
    try:
        # 使用官方推荐的方法获取屏幕尺寸
        display = Device.display()
        SCREEN_WIDTH = display.widthPixels
        SCREEN_HEIGHT = display.heightPixels
        print(f"获取屏幕尺寸成功: {SCREEN_WIDTH}x{SCREEN_HEIGHT}")
        return SCREEN_WIDTH, SCREEN_HEIGHT
    except Exception as e:
        print(f"获取屏幕尺寸出错: {e}")
        try:
            # 备用方法：使用shell命令
            info = system.shell("wm size")
            # 解析输出，格式通常为 "Physical size: 1080x2340"
            size = info.strip().split(":")[-1].strip().split("x")
            SCREEN_WIDTH = int(size[0])
            SCREEN_HEIGHT = int(size[1])
            print(f"使用备用方法获取屏幕尺寸: {SCREEN_WIDTH}x{SCREEN_HEIGHT}")
            return SCREEN_WIDTH, SCREEN_HEIGHT
        except Exception as e2:
            print(f"备用方法也失败: {e2}，使用默认值")
            # 默认值
            SCREEN_WIDTH = 720
            SCREEN_HEIGHT = 1280
            return SCREEN_WIDTH, SCREEN_HEIGHT

def swipe_up(ble, count=1):
    """向上滑动浏览视频，使用随机坐标模拟真人操作，增加滑动距离和速度
    
    Args:
        ble: HID设备对象
        count: 滑动次数
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    for i in range(count):
        print(f"正在滑动第 {i+1}/{count} 个视频")
        
        # 随机化起点和终点坐标，模拟真人操作
        # X坐标在屏幕中央附近随机
        start_x = screen_width // 2 + random.randint(-30, 30)
        # 确保X坐标不超出屏幕范围
        start_x = max(10, min(start_x, screen_width - 10))
        
        # Y坐标随机化，但保持在下半部分，增加起点位置更靠下
        start_y = int(screen_height * (0.8 + random.random() * 0.15))
        
        # 终点X坐标也有轻微随机偏移，但与起点保持接近
        end_x = start_x + random.randint(-20, 20)
        # 确保X坐标不超出屏幕范围
        end_x = max(10, min(end_x, screen_width - 10))
        
        # 终点Y坐标在上半部分随机，降低终点位置，增加滑动距离
        end_y = int(screen_height * (0.05 + random.random() * 0.1))
        
        # 减少滑动时间，增加滑动速度
        duration = random.randint(300, 600)
        
        print(f"滑动参数: 从({start_x}, {start_y})到({end_x}, {end_y}), 持续{duration}ms")
        # 执行滑动操作
        ble.slide(start_x, start_y, end_x, end_y, dur=duration)
        
        # 随机等待时间，模拟真实浏览
        wait_time = random.uniform(MIN_VIDEO_TIME, MAX_VIDEO_TIME)
        print(f"观看视频 {wait_time:.1f} 秒")
        time.sleep(wait_time)

def click_like(ble):
    """点赞当前视频"""
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 点赞按钮通常在屏幕右侧
    like_x = int(screen_width * 0.9)
    like_y = int(screen_height * 0.5)
    
    print("点赞当前视频")
    ble.click(like_x, like_y)
    time.sleep(0.5)

def click_follow(ble):
    """关注当前视频作者"""
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 关注按钮通常在屏幕右侧
    follow_x = int(screen_width * 0.9)
    follow_y = int(screen_height * 0.4)
    
    print("关注视频作者")
    ble.click(follow_x, follow_y)
    time.sleep(0.5)

def add_comment(ble):
    """在当前视频添加评论"""
    if not COMMENTS or not ENABLE_COMMENTING:
        return
        
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 评论按钮通常在屏幕底部中央
    comment_x = int(screen_width * 0.5)
    comment_y = int(screen_height * 0.9)
    
    print("准备添加评论")
    
    # 先尝试打开评论区
    if open_comment_section(ble):
        time.sleep(1)
        
        try:
            # 点击评论输入框（通常在底部中间）
            input_x = int(screen_width * 0.5)
            input_y = int(screen_height * 0.95)
            print("点击评论输入框")
            # 这里不使用safe_click，因为我们就是要点击输入框
            ble.click(input_x, input_y)
            time.sleep(1.5)
            
            # 设置ascript输入法为默认输入法
            set_ascript_input_method()
            time.sleep(0.5)
            
            # 输入随机评论
            comment = random.choice(COMMENTS)
            print(f"输入评论: {comment}")
            # 由于HID设备只能输入数字与英文，无法直接输入中文
            # 这里我们使用模拟按键输入英文评论
            print("警告：HID设备无法输入中文，将使用默认英文评论")
            english_comments = ["Great video!", "Awesome!", "I like it!", "Cool!", "Nice!"]
            english_comment = random.choice(english_comments)
            for char in english_comment:
                ble.type(char)
                time.sleep(0.1)
            time.sleep(1)
            
            # 点击发送按钮（通常在评论框右侧）
            send_x = int(screen_width * 0.9)
            send_y = int(screen_height * 0.95)
            print("点击发送按钮")
            ble.click(send_x, send_y)
            time.sleep(1.5)
            
            # 评论发送后，等待一会儿看到评论发布成功
            time.sleep(1)
        except Exception as e:
            print(f"添加评论时出错: {e}")
        finally:
            # 无论如何都要返回视频页面
            # 返回视频页面（点击返回按钮）
            print("返回视频页面")
            back_x = int(screen_width * 0.1)  # 左上角返回按钮
            back_y = int(screen_height * 0.05)
            safe_click(ble, back_x, back_y, "返回视频页面")
            time.sleep(1)
            
            # 确保返回成功
            ble.back()
            time.sleep(0.5)

def collect_coins(ble):
    """收集金币"""
    if not AUTO_COLLECT_COINS:
        return
        
    print("尝试收集金币...")
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 首先退回到首页
    for _ in range(2):
        ble.back()
        time.sleep(1)
    
    # 点击底部"赚钱"按钮（通常在底部菜单栏中间偏右）
    earn_x = int(screen_width * 0.7)
    earn_y = int(screen_height * 0.95)
    ble.click(earn_x, earn_y)
    time.sleep(3)
    
    # 点击"金币收益"区域（通常在上方）
    coin_x = int(screen_width * 0.5)
    coin_y = int(screen_height * 0.2)
    ble.click(coin_x, coin_y)
    time.sleep(2)
    
    # 点击"立即收取"按钮
    collect_x = int(screen_width * 0.5)
    collect_y = int(screen_height * 0.6)
    ble.click(collect_x, collect_y)
    time.sleep(1)
    
    # 返回到视频页面
    for _ in range(2):
        ble.back()
        time.sleep(1)
    
    # 点击底部"推荐"回到视频流
    recommend_x = int(screen_width * 0.2)
    recommend_y = int(screen_height * 0.95)
    ble.click(recommend_x, recommend_y)
    time.sleep(2)

def force_swipe_up(ble):
    """强力向上滑动，确保视频切换，但仍有随机性模拟真人
    
    Args:
        ble: HID设备对象
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 随机化起点和终点坐标，但保持较大的滑动距离
    # X坐标在屏幕中央附近随机
    start_x = screen_width // 2 + random.randint(-20, 20)
    # 确保X坐标不超出屏幕范围
    start_x = max(10, min(start_x, screen_width - 10))
    
    # Y坐标随机化，但保持在最下部分，增加起点位置更靠下
    start_y = int(screen_height * (0.9 + random.random() * 0.08))
    
    # 终点X坐标也有轻微随机偏移
    end_x = start_x + random.randint(-10, 10)
    # 确保X坐标不超出屏幕范围
    end_x = max(10, min(end_x, screen_width - 10))
    
    # 终点Y坐标在最上部分随机，降低终点位置，增加滑动距离
    end_y = int(screen_height * (0.02 + random.random() * 0.05))
    
    # 减少滑动时间，增加滑动速度
    duration = random.randint(200, 400)
    
    print(f"强力滑动参数: 从({start_x}, {start_y})到({end_x}, {end_y}), 持续{duration}ms")
    # 执行滑动
    ble.slide(start_x, start_y, end_x, end_y, dur=duration)
    time.sleep(1)  # 等待动画完成

def handle_stuck_situation(ble):
    """处理可能卡住的情况
    
    Args:
        ble: HID设备对象
    """
    print("检测到可能卡住，尝试恢复...")
    
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 尝试点击屏幕中央，唤醒可能的休眠状态
    center_x = screen_width // 2
    center_y = screen_height // 2
    ble.click(center_x, center_y)
    time.sleep(1)
    
    # 尝试按返回键
    ble.back()
    time.sleep(1)
    
    # 尝试强力滑动
    force_swipe_up(ble)
    
    # 如果还是不行，尝试重启应用
    print("尝试重新启动应用...")
    # 按Home键
    ble.home()
    time.sleep(1)
    
    # 重新启动应用
    start_app()
    time.sleep(5)

def random_touch(ble, x_range=(0.3, 0.7), y_range=(0.3, 0.7)):
    """随机点击屏幕某个区域，模拟偶尔的误触
    
    Args:
        ble: HID设备对象
        x_range: X坐标范围，相对于屏幕宽度的比例
        y_range: Y坐标范围，相对于屏幕高度的比例
    """
    screen_width, screen_height = get_screen_size()
    
    # 在指定范围内随机选择坐标
    x = int(screen_width * random.uniform(x_range[0], x_range[1]))
    y = int(screen_height * random.uniform(y_range[0], y_range[1]))
    
    print(f"随机点击屏幕位置: ({x}, {y})")
    ble.click(x, y)
    time.sleep(random.uniform(0.3, 1.0))

def double_click_like(ble):
    """双击点赞，模拟真人快速点赞操作
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 点赞区域在视频中央偏右位置
    like_x = int(screen_width * (0.5 + random.uniform(-0.05, 0.05)))
    like_y = int(screen_height * (0.5 + random.uniform(-0.05, 0.05)))
    
    print(f"双击点赞: ({like_x}, {like_y})")
    # 执行双击操作
    ble.click(like_x, like_y)
    time.sleep(random.uniform(0.1, 0.2))  # 真人双击的间隔很短
    ble.click(like_x, like_y)

def open_comment_section(ble):
    """打开并浏览评论区
    
    Args:
        ble: HID设备对象
        
    Returns:
        bool: 是否成功打开评论区
    """
    # 如果浏览功能被禁用，直接返回False
    if not ENABLE_BROWSING:
        print("浏览功能已禁用，不打开评论区")
        return False
        
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    print("尝试打开评论区...")
    
    # 方法1: 点击评论图标（通常在屏幕右侧）
    comment_icon_x = int(screen_width * 0.9)
    comment_icon_y = int(screen_height * 0.65)
    print("方法1: 点击评论图标")
    # 这里不使用safe_click，因为我们就是要点击评论图标
    ble.click(comment_icon_x, comment_icon_y)
    time.sleep(2.5)  # 给足够时间打开评论区
    
    # 如果方法1失败，尝试方法2
    # 方法2: 点击屏幕底部（评论区通常在底部）
    # 但要避开最底部的评论输入框区域
    comment_area_x = int(screen_width * 0.5)
    comment_area_y = int(screen_height * 0.85)  # 稍微上移，避开输入框
    print("方法2: 点击评论区域")
    safe_click(ble, comment_area_x, comment_area_y, "点击评论区域")
    time.sleep(2)
    
    # 如果方法2也失败，尝试方法3
    # 方法3: 上滑操作（从底部向上滑动以显示评论区）
    swipe_start_x = int(screen_width * 0.5)
    swipe_start_y = int(screen_height * 0.75)  # 从屏幕3/4处开始上滑
    swipe_end_x = swipe_start_x
    swipe_end_y = int(screen_height * 0.25)  # 滑到屏幕1/4处
    print("方法3: 上滑显示评论区")
    # 增加滑动持续时间，从800ms增加到1500-2000ms，使滑动更慢更自然
    ble.slide(swipe_start_x, swipe_start_y, swipe_end_x, swipe_end_y, dur=random.randint(1500, 2000))
    time.sleep(2)
    
    print("评论区应该已打开")
    # 点击一下评论区中部，确认已进入评论区，同时避开底部输入框
    confirm_x = int(screen_width * 0.5)
    confirm_y = int(screen_height * 0.5)  # 屏幕中部
    safe_click(ble, confirm_x, confirm_y, "确认评论区已打开")
    time.sleep(0.5)
    
    return True

def is_comment_section_open():
    """检测评论区是否已打开
    
    Returns:
        bool: 评论区是否已打开
    """
    try:
        # 这里可以通过截图分析来判断
        # 但由于截图分析复杂度较高，我们简单返回True
        # 在实际使用中，可以根据需要完善这个函数
        return True
    except Exception as e:
        print(f"检测评论区状态出错: {e}")
        return False

def browse_comment_section(ble):
    """浏览评论区
    
    Args:
        ble: HID设备对象
    """
    # 如果浏览功能被禁用，直接返回
    if not ENABLE_BROWSING:
        return
        
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 打开评论区
    print("准备浏览评论区...")
    
    # 确保评论区已打开
    if not open_comment_section(ble):
        print("无法打开评论区，返回")
        return
    
    # 浏览评论区
    print("开始浏览评论区内容...")
    
    # 等待评论区完全加载
    time.sleep(1.5)
    
    try:
        # 上下滑动浏览评论 - 注意避开底部评论输入框区域
        for i in range(random.randint(1, 3)):
            # 从中部往上滑动查看更多评论，避开底部输入框区域
            swipe_start_x = int(screen_width * 0.5)
            # 避开底部30%的区域（评论输入框通常在这里）
            swipe_start_y = int(screen_height * 0.55)
            swipe_end_x = swipe_start_x
            swipe_end_y = int(screen_height * 0.25)
            print(f"滑动浏览评论 {i+1}")
            # 增加滑动持续时间，从800ms增加到1800-2500ms，使滑动更慢更自然
            ble.slide(swipe_start_x, swipe_start_y, swipe_end_x, swipe_end_y, dur=random.randint(1800, 2500))
            # 增加滑动后的等待时间，给用户更多时间查看评论
            time.sleep(random.uniform(2, 3.5))
        
        # 随机点赞某条评论 - 确保点击在评论内容区域，避开底部
        if random.random() < 0.3:  # 30%概率点赞评论
            # 点赞按钮通常在评论右侧
            like_x = int(screen_width * 0.9)
            # 选择在屏幕上部的评论，避开底部输入框
            like_y = int(screen_height * (0.3 + random.random() * 0.2))
            print("点赞某条评论")
            ble.click(like_x, like_y)
            time.sleep(0.5)
    except Exception as e:
        print(f"浏览评论区出错: {e}")
    finally:
        # 无论如何都要确保关闭评论区
        print("关闭评论区...")
        # 尝试点击返回按钮
        back_x = int(screen_width * 0.1)
        back_y = int(screen_height * 0.05)
        safe_click(ble, back_x, back_y, "关闭评论区")
        time.sleep(1)
        
        # 如果可能仍在评论区，再次尝试返回
        if is_comment_section_open():
            print("可能仍在评论区，再次尝试返回")
            ble.back()  # 使用系统返回键
            time.sleep(1)

def safe_click(ble, x, y, description="点击"):
    """安全点击函数，避免点击到分享等敏感区域
    
    Args:
        ble: HID设备对象
        x: 点击的X坐标
        y: 点击的Y坐标
        description: 点击操作的描述
        
    Returns:
        bool: 是否安全点击成功
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 定义危险区域（分享按钮等通常在右侧）
    danger_zones = [
        # 右侧区域（分享按钮区）
        {"x_min": int(screen_width * 0.8), "x_max": screen_width, 
         "y_min": int(screen_height * 0.4), "y_max": int(screen_height * 0.7)},
        # 底部输入框区域
        {"x_min": 0, "x_max": screen_width, 
         "y_min": int(screen_height * 0.9), "y_max": screen_height}
    ]
    
    # 检查是否在危险区域
    for zone in danger_zones:
        if (zone["x_min"] <= x <= zone["x_max"] and 
            zone["y_min"] <= y <= zone["y_max"]):
            print(f"警告：尝试点击的位置({x},{y})在危险区域，调整点击位置")
            # 调整为安全位置（屏幕中央偏左）
            x = int(screen_width * 0.3)
            y = int(screen_height * 0.5)
            break
    
    print(f"{description}: ({x},{y})")
    ble.click(x, y)
    return True

def random_safe_click(ble, description="随机点击"):
    """在安全区域内随机点击
    
    Args:
        ble: HID设备对象
        description: 点击操作的描述
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 在安全区域内随机选择坐标（避开右侧和底部）
    x = int(screen_width * (0.2 + random.random() * 0.5))
    y = int(screen_height * (0.2 + random.random() * 0.6))
    
    return safe_click(ble, x, y, description)

def watch_video_with_interaction(ble):
    """观看视频时的互动，模拟真人观看视频时的行为
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 总观看时间
    total_watch_time = random.uniform(MIN_VIDEO_TIME, MAX_VIDEO_TIME)
    print(f"计划观看视频 {total_watch_time:.1f} 秒")
    
    # 已观看时间
    watched_time = 0
    
    while watched_time < total_watch_time:
        # 随机决定下一个动作
        if not ENABLE_BROWSING:
            # 浏览功能关闭时，不包含check_comments动作选项
            action_type = random.choices(
                ["nothing", "tap_screen", "small_swipe"],
                weights=[0.9, 0.07, 0.03],  # 降低轻触屏幕概率从0.15到0.07
                k=1
            )[0]
        else:
            # 浏览功能开启时，包含所有动作选项
            action_type = random.choices(
                ["nothing", "tap_screen", "small_swipe", "check_comments"],
                weights=[0.85, 0.07, 0.03, 0.05],  # 降低轻触屏幕概率从0.15到0.07
                k=1
            )[0]
        
        # 执行动作
        if action_type == "nothing":
            # 不做任何操作，继续观看
            wait_time = random.uniform(2, 5)
            time.sleep(wait_time)
            watched_time += wait_time
        
        elif action_type == "tap_screen":
            # 轻触屏幕，暂停/继续播放
            # 使用安全点击函数
            x = int(screen_width * (0.3 + random.random() * 0.4))
            y = int(screen_height * (0.4 + random.random() * 0.2))
            safe_click(ble, x, y, "轻触屏幕")
            
            # 随机决定是否需要再次点击（模拟暂停后继续播放）
            # 降低二次点击概率从30%到20%
            if random.random() < 0.2:
                wait_time = random.uniform(1, 3)
                time.sleep(wait_time)
                # 再次点击继续播放，但位置有随机偏移
                # 在原位置基础上添加随机偏移，模拟真人不会点击完全相同位置
                offset_x = random.randint(-50, 50)
                offset_y = random.randint(-50, 50)
                new_x = max(10, min(x + offset_x, screen_width - 10))
                new_y = max(10, min(y + offset_y, screen_height - 10))
                safe_click(ble, new_x, new_y, "继续播放")
                watched_time += wait_time + 0.5
            else:
                # 不需要二次点击的情况
                wait_time = random.uniform(0.5, 1.5)
                time.sleep(wait_time)
                watched_time += wait_time
        
        elif action_type == "small_swipe":
            # 小幅滑动，查看视频进度条或微调位置
            # 严格限制在屏幕左侧区域，远离分享按钮
            x1 = int(screen_width * (0.1 + random.random() * 0.25))  # 限制在屏幕左侧1/4区域
            y1 = int(screen_height * (0.3 + random.random() * 0.4))  # 避开底部和顶部
            
            # 增加滑动距离，确保不会被误认为是点击操作
            # 主要在垂直方向滑动，增加垂直方向的滑动距离
            x2 = x1 + random.randint(-30, 30)
            y2 = y1 + random.randint(-100, 100)  # 增加垂直滑动距离
            
            # 确保坐标不超出屏幕范围，并且仍然在屏幕左侧安全区域
            x2 = max(10, min(x2, int(screen_width * 0.4)))  # 限制在屏幕左侧40%区域内
            y2 = max(int(screen_height * 0.2), min(y2, int(screen_height * 0.8)))
            
            print(f"小幅滑动屏幕: ({x1},{y1}) -> ({x2},{y2})")
            # 增加滑动持续时间，确保系统能识别为滑动而非点击
            ble.slide(x1, y1, x2, y2, dur=random.randint(400, 600))
            wait_time = random.uniform(2, 3)  # 增加等待时间，避免连续滑动
            time.sleep(wait_time)
            watched_time += wait_time + 0.5
        
        elif action_type == "check_comments":
            # 如果浏览功能已禁用，则跳过查看评论
            if not ENABLE_BROWSING:
                # 替代为其他动作
                wait_time = random.uniform(2, 4)
                time.sleep(wait_time)
                watched_time += wait_time
                continue
                
            # 查看评论区然后返回
            print("尝试查看评论区")
            try:
                # 减少浏览评论区的时间，避免长时间停留
                # 打开评论区
                if open_comment_section(ble):
                    # 简单浏览一下就返回，不做复杂操作
                    time.sleep(1)
                    
                    # 只做一次简单的滑动，但使用更慢的滑动速度
                    swipe_start_x = int(screen_width * 0.5)
                    swipe_start_y = int(screen_height * 0.5)  # 从中间开始滑动
                    swipe_end_x = swipe_start_x
                    swipe_end_y = int(screen_height * 0.3)
                    # 增加滑动持续时间，从500ms增加到1500-2000ms
                    ble.slide(swipe_start_x, swipe_start_y, swipe_end_x, swipe_end_y, dur=random.randint(1500, 2000))
                    time.sleep(1.5)  # 增加等待时间
                    
                    # 快速返回视频页面
                    back_x = int(screen_width * 0.1)
                    back_y = int(screen_height * 0.05)
                    safe_click(ble, back_x, back_y, "返回视频页面")
                    time.sleep(1)
                    
                    # 确保返回成功
                    ble.back()
                    
                watched_time += 5  # 大约花费5秒
            except Exception as e:
                print(f"查看评论区时出错: {e}")
                # 尝试返回视频页面
                try:
                    ble.back()
                    time.sleep(1)
                except:
                    pass
                watched_time += 1
    
    print(f"视频观看完成，实际观看时间: {watched_time:.1f} 秒")
    return watched_time

def is_really_stuck(ble):
    """更准确地检测是否真的卡住了
    
    通过检查屏幕变化、尝试轻微操作等方式来判断是否真的卡住
    
    Args:
        ble: HID设备对象
        
    Returns:
        bool: 是否真的卡住了
    """
    print("检查是否真的卡住...")
    
    try:
        # 获取屏幕尺寸
        screen_width, screen_height = get_screen_size()
        
        # 1. 先截取当前屏幕
        try:
            from ascript.android.screen import capture
            img1 = capture()
            print("截取第一张屏幕")
        except:
            print("无法截取屏幕，跳过截图比对")
            img1 = None
        
        # 2. 尝试轻微滑动，不影响用户体验
        small_x = screen_width // 2
        small_y1 = int(screen_height * 0.55)
        small_y2 = int(screen_height * 0.45)
        print("执行轻微滑动测试...")
        ble.slide(small_x, small_y1, small_x, small_y2, dur=300)
        time.sleep(1)
        
        # 3. 再截取一次屏幕，比较两次截图是否有变化
        if img1:
            try:
                img2 = capture()
                print("截取第二张屏幕")
                
                # 简单比较两张图片是否完全相同
                if img1.tobytes() == img2.tobytes():
                    print("屏幕没有变化，可能真的卡住了")
                    return True
                else:
                    print("屏幕有变化，没有卡住")
                    return False
            except:
                print("第二次截图失败，无法比较")
        
        # 4. 尝试检测当前应用是否仍然是快手
        try:
            app_info = Device.currentAppInfo()
            if APP_PACKAGE not in app_info.packageName:
                print(f"当前应用不是快手，而是: {app_info.packageName}")
                return True
            else:
                print("当前应用仍然是快手，可能没有卡住")
        except:
            print("无法获取当前应用信息")
        
        # 如果以上检测都无法确定，返回False表示可能没卡住
        return False
    except Exception as e:
        print(f"检测卡住时出错: {e}")
        return False

def swipe_to_next_video(ble):
    """向上滑动到下一个视频，增强版本确保切换成功
    
    Args:
        ble: HID设备对象
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    print("滑动到下一个视频")
    
    # 随机化起点和终点坐标，模拟真人操作
    # X坐标在屏幕中央附近随机
    start_x = screen_width // 2 + random.randint(-20, 20)
    # 确保X坐标不超出屏幕范围
    start_x = max(10, min(start_x, screen_width - 10))
    
    # Y坐标随机化，但保持在下半部分，增加起点位置更靠下
    start_y = int(screen_height * (0.85 + random.random() * 0.1))
    
    # 终点X坐标也有轻微随机偏移，但与起点保持接近
    end_x = start_x + random.randint(-10, 10)
    # 确保X坐标不超出屏幕范围
    end_x = max(10, min(end_x, screen_width - 10))
    
    # 终点Y坐标在上半部分随机，降低终点位置，增加滑动距离
    end_y = int(screen_height * (0.05 + random.random() * 0.05))
    
    # 减少滑动时间，增加滑动速度
    duration = random.randint(200, 400)
    
    print(f"滑动参数: 从({start_x}, {start_y})到({end_x}, {end_y}), 持续{duration}ms")
    # 执行滑动操作
    ble.slide(start_x, start_y, end_x, end_y, dur=duration)
    
    # 等待视频加载
    time.sleep(random.uniform(1, 2))
    
    # 检查是否成功切换视频，如果没有，尝试再次滑动
    if random.random() < 0.2:  # 20%概率再次滑动，模拟第一次滑动不成功的情况
        print("确保视频切换，再次滑动...")
        time.sleep(0.5)
        # 使用更快更长的滑动
        ble.slide(start_x, int(screen_height * 0.9), end_x, int(screen_height * 0.02), dur=150)
        time.sleep(1)

def like_video(ble):
    """点赞当前视频
    
    Args:
        ble: HID设备对象
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 点赞按钮通常在屏幕右侧
    like_x = int(screen_width * 0.9)
    like_y = int(screen_height * 0.5)
    
    print("点赞当前视频")
    # 这里不使用safe_click，因为我们就是要点击点赞按钮
    ble.click(like_x, like_y)
    time.sleep(0.5)

def follow_user(ble):
    """关注当前视频作者
    
    Args:
        ble: HID设备对象
    """
    # 获取屏幕尺寸
    screen_width, screen_height = get_screen_size()
    
    # 关注按钮通常在屏幕右侧
    follow_x = int(screen_width * 0.9)
    follow_y = int(screen_height * 0.4)
    
    print("关注视频作者")
    # 这里不使用safe_click，因为我们就是要点击关注按钮
    ble.click(follow_x, follow_y)
    time.sleep(0.5)

def is_stuck(ble):
    """检测是否卡住
    
    Args:
        ble: HID设备对象
        
    Returns:
        bool: 是否卡住
    """
    return is_really_stuck(ble)

def recover_from_stuck(ble):
    """从卡住状态恢复
    
    Args:
        ble: HID设备对象
        
    Returns:
        bool: 是否成功恢复
    """
    try:
        print("尝试从卡住状态恢复...")
        handle_stuck_situation(ble)
        # 简单检查是否恢复成功
        time.sleep(2)
        if not is_really_stuck(ble):
            print("恢复成功")
            return True
        else:
            print("恢复失败，可能需要手动干预")
            return False
    except Exception as e:
        print(f"恢复过程中出错: {e}")
        return False

def main():
    """主函数"""
    start_time = datetime.datetime.now()
    print(f"快手极速版自动化脚本已启动，当前时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"总观看时长设置为: {MAX_RUN_TIME//60} 分钟")
    
    # 初始化设置ascript输入法
    print("初始化输入法设置...")
    set_ascript_input_method()
    
    # 连接HID设备
    ble_device = connect_hid()
    
    if ble_device:
        # 启动快手极速版
        if start_app():
            # 等待应用完全加载
            load_wait = random.uniform(3, 6)  # 随机等待时间
            print(f"等待应用加载 {load_wait:.1f} 秒")
            time.sleep(load_wait)
            
            # 浏览视频
            try:
                # 记录上次操作时间
                last_action_time = time.time()
                # 记录上次检测卡住的时间，避免频繁检测
                last_stuck_check_time = time.time()
                # 记录脚本开始运行时间
                script_start_time = time.time()
                
                # 浏览视频
                for i in range(MAX_VIDEOS):
                    # 检查是否达到最大运行时间
                    if time.time() - script_start_time >= MAX_RUN_TIME:
                        print(f"已达到设定的最大运行时间 {MAX_RUN_TIME//60} 分钟，停止执行")
                        break
                        
                    print(f"正在浏览第 {i+1}/{MAX_VIDEOS} 个视频")
                    
                    # 先等待一小段时间，让视频开始播放
                    time.sleep(random.uniform(1, 3))
                    
                    # 随机决定是双击点赞还是普通操作
                    if random.random() < 0.2:  # 20%概率使用双击点赞
                        double_click_like(ble_device)
                        # 如果双击点赞了，就不再执行普通点赞
                        actions = []
                        # 根据功能开关添加操作
                        if ENABLE_COMMENTING:
                            actions.append("comment")
                        if ENABLE_BROWSING:
                            actions.append("follow")  # 关注操作也属于浏览互动
                        random.shuffle(actions)
                    else:
                        # 随机决定操作顺序，更像真人操作
                        actions = ["like"]  # 点赞基本功能始终保留
                        # 根据功能开关添加其他操作
                        if ENABLE_BROWSING:
                            actions.append("follow")
                        if ENABLE_COMMENTING:
                            actions.append("comment")
                        random.shuffle(actions)
                    
                    # 偶尔会有误触行为
                    if random.random() < 0.1:  # 10%概率发生误触
                        random_touch(ble_device)
                    
                    for action in actions:
                        # 随机等待一小段时间再执行操作
                        time.sleep(random.uniform(0.5, 2))
                        
                        if action == "like" and random.random() < LIKE_PROBABILITY:
                            like_video(ble_device)
                        elif action == "follow" and random.random() < FOLLOW_PROBABILITY:
                            follow_user(ble_device)
                        elif action == "comment" and random.random() < COMMENT_PROBABILITY:
                            add_comment(ble_device)
                    
                    # 使用互动方式观看视频
                    watch_time = watch_video_with_interaction(ble_device)
                    
                    # 每浏览5个视频，尝试收集一次金币
                    if i > 0 and i % 5 == 0:
                        # 有50%概率收集金币
                        if random.random() < 0.5:
                            collect_coins(ble_device)
                    
                    # 随机决定使用哪种滑动方式
                    if random.random() < 0.7:  # 70%概率使用普通滑动
                        swipe_up(ble_device, count=1)
                    else:  # 30%概率使用强力滑动
                        force_swipe_up(ble_device)
                    
                    # 检查滑动是否成功，如果失败则重试
                    retry_count = 0
                    max_retries = 3
                    while retry_count < max_retries:
                        # 简单检查是否仍在同一视频（通过截图或其他方式）
                        # 这里使用随机概率模拟检测，实际应用中应该使用更准确的方法
                        if random.random() < 0.1:  # 假设有10%的概率滑动失败
                            print(f"滑动可能失败，尝试重新滑动 (尝试 {retry_count+1}/{max_retries})")
                            # 使用更强力的滑动方式
                            force_swipe_up(ble_device)
                            retry_count += 1
                            time.sleep(1)
                        else:
                            # 滑动成功，跳出循环
                            break
                    
                    if retry_count == max_retries:
                        print("多次尝试滑动失败，可能需要检查网络或应用状态")
                    
                    # 更新上次操作时间
                    last_action_time = time.time()
                    
                    # 检查是否卡住（超过60秒没有操作，且至少30秒没有检测过卡住）
                    current_time = time.time()
                    if (current_time - last_action_time > 60 and 
                        current_time - last_stuck_check_time > 30):
                        
                        # 更新上次检测卡住的时间
                        last_stuck_check_time = current_time
                        
                        # 使用更准确的方法检测是否真的卡住
                        if is_really_stuck(ble_device):
                            print("检测到真的卡住了，尝试恢复...")
                            handle_stuck_situation(ble_device)
                        else:
                            print("虽然长时间没操作，但没有真正卡住，继续执行")
                
                print("视频浏览任务完成")
                
                # 最后再收集一次金币
                collect_coins(ble_device)
            except Exception as e:
                print(f"视频浏览过程中出错: {e}")
                # 尝试恢复
                try:
                    handle_stuck_situation(ble_device)
                except:
                    pass
            finally:
                end_time = datetime.datetime.now()
                duration = (end_time - start_time).total_seconds()
                print(f"脚本运行结束，共运行了 {duration:.2f} 秒")
        else:
            print("快手极速版启动失败")
    else:
        print("无法继续执行，请确保ESP32 HID设备已正确配对")

if __name__ == "__main__":
    main() 